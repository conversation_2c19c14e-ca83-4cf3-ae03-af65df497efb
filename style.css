/* ==========================================================================
   1. Modern Dark Theme Redesign
   ========================================================================== */

/* -- Переменные для цветов и шрифтов -- */
:root {
    --bg-color: #0d1117;
    --primary-surface-color: #161b22;
    --secondary-surface-color: #21262d;
    --border-color: #30363d;
    --text-primary: #c9d1d9;
    --text-secondary: #8b949e;
    --accent-color: #58a6ff; /* Яркий синий */
    --accent-glow: rgba(88, 166, 255, 0.5);

    --font-headings: 'Exo 2', sans-serif;
    --font-body: 'Inter', sans-serif;
}

/* -- Общие стили -- */
body {
    font-family: var(--font-body);
    margin: 0;
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.7;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

h1, h2, h3 {
    font-family: var(--font-headings);
    color: #ffffff;
    font-weight: 700;
    letter-spacing: 1px;
}

h2 {
    text-align: center;
    margin-bottom: 60px;
    font-size: 2.8em;
    /* -- Градиентный текст для заголовков -- */
    background: linear-gradient(45deg, var(--accent-color), #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

section {
    padding: 80px 0;
    border-bottom: 1px solid var(--border-color);
    opacity: 0; /* Для анимации появления */
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

section.is-visible {
    opacity: 1;
    transform: translateY(0);
}

/* ==========================================================================
   2. Секции
   ========================================================================== */

/* -- Хедер -- */
.header {
    padding: 60px 0;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.header h1 {
    font-size: 2.5em;
    max-width: 900px;
    margin: 0 auto 16px auto;
}

.header p {
    font-size: 1.2em;
    color: var(--text-secondary);
}

/* -- Секция "До и После" -- */
.comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
    gap: 40px;
    /* -- Центрирование последних элементов -- */
    justify-content: center;
}

.comparison-card {
    background: var(--primary-surface-color);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 0 0 rgba(0,0,0,0); /* Начальное состояние тени */
}

.comparison-card:hover {
    transform: translateY(-10px);
    border-color: var(--accent-color);
    /* -- Эффект неонового свечения -- */
    box-shadow: 0 0 20px var(--accent-glow);
}

.comparison-card h3 {
    padding: 20px 0;
    margin: 0;
    background-color: var(--secondary-surface-color);
    font-size: 1.2em;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

/* -- Слайдер -- */
.image-comparison-slider {
    position: relative;
    width: 100%;
    height: 320px;
    overflow: hidden;
    background-color: #000;
}

.image-comparison-slider img {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: contain;
}

.image-comparison-slider .top-image {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
}

.image-comparison-slider .handle {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background-color: var(--accent-color);
    cursor: ew-resize;
    transform: translateX(-50%);
    z-index: 10;
}

.image-comparison-slider .handle::after {
    content: '\2194';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--accent-color);
    color: #000;
    padding: 10px 6px;
    border-radius: 8px;
    font-size: 1.3em;
    box-shadow: 0 0 15px var(--accent-glow);
}

/* -- Карта -- */
.map-section iframe {
    border-radius: 16px;
    border: 1px solid var(--border-color);
    filter: grayscale(100%) invert(90%) contrast(80%); /* Стилизация карты под темную тему */
}

/* -- Пояснение -- */
.details-content {
    max-width: 750px;
    margin: 0 auto;
    text-align: center;
    font-size: 1.1em;
}

.details-content p {
    margin-bottom: 20px;
    color: var(--text-secondary);
}

.details-content strong {
    color: var(--accent-color);
    font-weight: 600;
}

/* -- Контакты -- */
.contact-card {
    background: var(--secondary-surface-color);
    padding: 40px;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    max-width: 550px;
    margin: 0 auto;
    text-align: center;
}

.contact-card ul {
    list-style: none;
    padding: 0;
}

.contact-card li {
    margin-bottom: 15px;
    font-size: 1.2em;
}

.contact-card a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease, text-shadow 0.3s ease;
}

.contact-card a:hover {
    color: #fff;
    text-shadow: 0 0 10px var(--accent-glow);
}

/* -- Футер -- */
.footer {
    background-color: var(--bg-color);
    color: var(--text-secondary);
    text-align: center;
    padding: 40px 0;
    font-size: 0.9em;
    border-top: 1px solid var(--border-color);
}

/* ==========================================================================
   3. Адаптивность
   ========================================================================== */

@media (max-width: 768px) {
    h2 { font-size: 2.2em; }
    .header h1 { font-size: 2em; }
    section { padding: 60px 0; }
}

@media (max-width: 480px) {
    .comparison-grid { grid-template-columns: 1fr; }
    h2 { font-size: 1.8em; }
    .header h1 { font-size: 1.6em; }
}