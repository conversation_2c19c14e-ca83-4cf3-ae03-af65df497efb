document.addEventListener('DOMContentLoaded', () => {
    // --- 1. Логика для слайдера "До/После" ---
    const sliders = document.querySelectorAll('.image-comparison-slider');

    sliders.forEach(slider => {
        const topImage = slider.querySelector('img:last-of-type');
        const handle = document.createElement('div');
        handle.classList.add('handle');
        
        const topImageClone = topImage.cloneNode();
        topImageClone.classList.add('top-image');
        slider.appendChild(topImageClone);
        slider.appendChild(handle);

        let isDragging = false;

        const startDrag = (e) => {
            isDragging = true;
            slider.classList.add('is-dragging');
            e.preventDefault();
        };

        const stopDrag = () => {
            isDragging = false;
            slider.classList.remove('is-dragging');
        };

        const onDrag = (e) => {
            if (!isDragging) return;

            const sliderRect = slider.getBoundingClientRect();
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            if (clientX === undefined) return;

            let x = clientX - sliderRect.left;
            x = Math.max(0, Math.min(x, sliderRect.width));

            const percentage = (x / sliderRect.width) * 100;

            handle.style.left = `${percentage}%`;
            topImageClone.style.clipPath = `polygon(0 0, ${percentage}% 0, ${percentage}% 100%, 0 100%)`;
        };

        slider.addEventListener('mousedown', startDrag);
        slider.addEventListener('touchstart', startDrag, { passive: false });

        document.addEventListener('mouseup', stopDrag);
        document.addEventListener('touchend', stopDrag);
        
        document.addEventListener('mousemove', onDrag);
        document.addEventListener('touchmove', onDrag, { passive: false });
    });

    // --- 2. Анимация появления секций при скролле ---
    const sections = document.querySelectorAll('section');

    const observerOptions = {
        root: null, // viewport
        rootMargin: '0px',
        threshold: 0.1 // Секция считается видимой, если видна на 10%
    };

    const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('is-visible');
                observer.unobserve(entry.target); // Отключаем наблюдение после анимации
            }
        });
    }, observerOptions);

    sections.forEach(section => {
        observer.observe(section);
    });
});