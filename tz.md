## 📄 Техническое задание: Статичный лендинг для проекта реконструкции дорог

### 🧩 Цель:

Создать одностраничный сайт (landing page), размещаемый на GitHub Pages. Страница должна рассказывать о проекте реконструкции дорог между многоквартирными домами. Упор на визуальные "до/после" фото, карту участка и краткое описание.

---

### 🎨 Общий стиль (UI/UX):

* Оптимистичный, но **не яркий** дизайн (теплые светлые тона: светло-бежевый, серо-голубой, зелёные акценты)
* Аккуратные закругления, мягкие тени
* Шрифт: комфортный для чтения (например, Inter, Roboto, Open Sans)
* Адаптивная вёрстка (обязательная мобильная версия)
* Без перегрузки анимациями

---

### 📌 Структура страницы:

#### 1. 🔷 Хедер (Header)

* Заголовок: **"Реконструкция внутриквартальных дорог в районе "Ростовская обл., г. Шахты, ул. Советская 231-237"**
* Подзаголовок: "Фотографии, план и ход выполнения дорожных работ"

#### 2. 🔷 Секция “До и После”

* Для каждого участка выводить:
  
  * Название/адрес (например: "Участок 1")
  
  * Компонент сравнения "до/после"
    
    * либо слайдер с ползунком (до/после),
    * либо два изображения рядом

* Использовать следующий список файлов:

##### 📂 Фото:

| Участок | До      | После      |
| -------:| ------- | ---------- |
| 1       | `1.jpg` | `1_1.jpeg` |
| 2       | `2.jpg` | `2_2.jpeg` |
| 3       | `3.jpg` | `3_3.jpeg` |
| 4       | `4.jpg` | `4_4.jpeg` |
| 5       | `5.jpg` | `5_5.jpeg` |

##### 📝 Примечания:

* Фото лежат в папке `/images/`
* Использовать относительные пути (`images/1.jpg` и т.п.)
* Изображения адаптировать под ширину экрана

#### 3. 🔷 Встроенная карта

* Использовать **Google Maps Embed API** через iframe
* Заголовок: “Карта расположения участков”
* Размер: 100% по ширине, \~450px по высоте
* Вставить заглушку с адресом или выдать инструкцию для замены

#### 4. 🔷 Пояснение

* Краткое описание:
  
  * Что делается и почему
  * Какой тип асфальта используется
  * Кто подрядчик
  * Сроки выполнения
  * Бюджет (по желанию)

#### 5. 🔷 Контакты / Обратная связь

* Электронная почта (пример: `<EMAIL>`)
* Telegram-иконка (пример: `@roadfix_bot`)
* Можно оформить как простую карточку или футер

#### 6. 🔷 Футер

* Подпись: “© 2025 Проект реконструкции дорог. Все права защищены.”

---

### 🧱 Используемые технологии:

* HTML5, CSS3
* JavaScript (только для before/after-слайдера)
* Google Fonts (шрифты)
* ❌ Без серверной части, полностью статично
* ✅ Готово для публикации через GitHub Pages

---

### 📦 Структура проекта:

```
/index.html
/style.css
/scripts.js           ← (если нужен слайдер)
/images/
  ├─ 1.jpg
  ├─ 1_1.jpeg
  ├─ 2.jpg
  ├─ 2_2.jpeg
  ├─ 3.jpg
  ├─ 3_3.jpeg
  ├─ 4.jpg
  ├─ 4_4.jpeg
  ├─ 5.jpg
  └─ 5_5.jpeg
```

---

### ✅ Цель:

На выходе должен получиться **эстетичный, легковесный, адаптивный сайт**, демонстрирующий положительные изменения от реконструкции дорог, с минимумом кода и максимумом наглядности.
